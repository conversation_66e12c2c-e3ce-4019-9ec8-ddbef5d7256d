/* Modern Portfolio CSS - Browser Compatible Version */
/* Author: <PERSON><PERSON> */

/* Custom Properties (CSS Variables) */
:root {
  /* Colors */
  --color-primary: #000000;
  --color-primary-dark: #1f2937;
  --color-primary-light: #374151;
  --color-secondary: #64748b;
  --color-accent: #f59e0b;
  --color-success: #10b981;
  --color-warning: #f59e0b;
  --color-error: #ef4444;

  /* Neutral Colors */
  --color-white: #ffffff;
  --color-gray-50: #f8fafc;
  --color-gray-100: #f1f5f9;
  --color-gray-200: #e2e8f0;
  --color-gray-300: #cbd5e1;
  --color-gray-400: #94a3b8;
  --color-gray-500: #64748b;
  --color-gray-600: #475569;
  --color-gray-700: #334155;
  --color-gray-800: #1e293b;
  --color-gray-900: #0f172a;

  /* Typography */
  --font-primary: "Lato", -apple-system, BlinkMacSystemFont, "Segoe UI",
    sans-serif;
  --font-heading: "Playfair Display", Georgia, serif;

  /* Font Sizes */
  --text-xs: 0.75rem;
  --text-sm: 0.875rem;
  --text-base: 1rem;
  --text-lg: 1.125rem;
  --text-xl: 1.25rem;
  --text-2xl: 1.5rem;
  --text-3xl: 1.875rem;
  --text-4xl: 2.25rem;
  --text-5xl: 3rem;
  --text-6xl: 3.75rem;

  /* Spacing */
  --space-1: 0.25rem;
  --space-2: 0.5rem;
  --space-3: 0.75rem;
  --space-4: 1rem;
  --space-5: 1.25rem;
  --space-6: 1.5rem;
  --space-8: 2rem;
  --space-10: 2.5rem;
  --space-12: 3rem;
  --space-16: 4rem;
  --space-20: 5rem;
  --space-24: 6rem;

  /* Shadows */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1),
    0 2px 4px -2px rgba(0, 0, 0, 0.1);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1),
    0 4px 6px -4px rgba(0, 0, 0, 0.1);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1),
    0 8px 10px -6px rgba(0, 0, 0, 0.1);

  /* Border Radius */
  --radius-sm: 0.25rem;
  --radius-md: 0.375rem;
  --radius-lg: 0.5rem;
  --radius-xl: 0.75rem;
  --radius-2xl: 1rem;
  --radius-full: 9999px;

  /* Transitions */
  --transition-fast: 150ms ease-in-out;
  --transition-normal: 250ms ease-in-out;
  --transition-slow: 350ms ease-in-out;

  /* Container Sizes */
  --container-sm: 640px;
  --container-md: 768px;
  --container-lg: 1024px;
  --container-xl: 1280px;
  --container-2xl: 1536px;
}

/* Base Styles */
body {
  font-family: var(--font-primary);
  color: var(--color-gray-800);
  background-color: var(--color-white);
  line-height: 1.6;
}

h1,
h2,
h3,
h4,
h5,
h6 {
  font-family: var(--font-heading);
  font-weight: 600;
  line-height: 1.2;
  color: var(--color-gray-900);
}

h1 {
  font-size: var(--text-5xl);
}
h2 {
  font-size: var(--text-4xl);
}
h3 {
  font-size: var(--text-3xl);
}
h4 {
  font-size: var(--text-2xl);
}
h5 {
  font-size: var(--text-xl);
}
h6 {
  font-size: var(--text-lg);
}

p {
  margin-bottom: var(--space-4);
  color: var(--color-gray-600);
}

a {
  color: var(--color-primary);
  transition: color var(--transition-fast);
}

a:hover {
  color: var(--color-primary-dark);
}

/* Container */
.container {
  width: 100%;
  max-width: var(--container-xl);
  margin: 0 auto;
  padding: 0 var(--space-4);
}

@media (min-width: 640px) {
  .container {
    padding: 0 var(--space-6);
  }
}

@media (min-width: 1024px) {
  .container {
    padding: 0 var(--space-8);
  }
}

/* Buttons */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: var(--space-3) var(--space-6);
  font-size: var(--text-base);
  font-weight: 500;
  border-radius: var(--radius-lg);
  text-decoration: none;
  transition: all var(--transition-fast);
  cursor: pointer;
  border: 2px solid transparent;
}

.btn--primary {
  background-color: var(--color-primary);
  color: var(--color-white);
}

.btn--primary:hover {
  background-color: var(--color-primary-dark);
  transform: translateY(-2px) scale(1.02);
  box-shadow: var(--shadow-xl);
}

.btn--secondary {
  background-color: transparent;
  color: var(--color-primary);
  border-color: var(--color-primary);
  position: relative;
  overflow: hidden;
}

.btn--secondary::before {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background-color: var(--color-primary);
  transition: left var(--transition-normal);
  z-index: -1;
}

.btn--secondary:hover {
  color: var(--color-white);
  transform: translateY(-2px) scale(1.02);
  box-shadow: var(--shadow-xl);
}

.btn--secondary:hover::before {
  left: 0;
}

.btn--full {
  width: 100%;
}

/* Header & Navigation */
.header {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  background-color: var(--color-gray-900);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid var(--color-gray-700);
  z-index: 1000;
}

.nav__container {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--space-4) var(--space-6);
  max-width: var(--container-xl);
  margin: 0 auto;
}

.nav__logo {
  font-family: var(--font-heading);
  font-size: var(--text-xl);
  font-weight: 600;
  color: var(--color-white);
}

.nav__toggle {
  display: none;
  flex-direction: column;
  gap: var(--space-1);
  padding: var(--space-2);
  cursor: pointer;
  border-radius: var(--radius-md);
  transition: all var(--transition-fast);
}

.nav__toggle:hover {
  background-color: rgba(248, 250, 252, 0.1);
}

.nav__toggle-line {
  width: 24px;
  height: 2px;
  background-color: var(--color-white);
  transition: all var(--transition-normal);
  border-radius: var(--radius-full);
}

.nav__menu {
  display: flex;
  gap: var(--space-8);
}

.nav__link {
  font-weight: 500;
  color: var(--color-white);
  transition: all var(--transition-normal);
  position: relative;
  padding: var(--space-2) var(--space-3);
  border-radius: var(--radius-md);
  text-decoration: none;
}

.nav__link::after {
  content: "";
  position: absolute;
  bottom: -2px;
  left: 50%;
  width: 0;
  height: 2px;
  background-color: var(--color-accent);
  transition: all var(--transition-normal);
  transform: translateX(-50%);
  border-radius: var(--radius-full);
}

.nav__link:hover {
  color: var(--color-accent);
  transform: translateY(-1px);
}

.nav__link:hover::after,
.nav__link.active::after {
  width: 80%;
}

.nav__link.active {
  color: var(--color-accent);
}

@media (max-width: 768px) {
  .nav__toggle {
    display: flex;
  }

  .nav__menu {
    display: none;
  }

  .nav__menu.active {
    display: flex;
    flex-direction: column;
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background-color: var(--color-gray-900);
    border-top: 1px solid var(--color-gray-700);
    padding: var(--space-4);
    box-shadow: var(--shadow-lg);
    animation: slideDown 0.3s ease-out;
    gap: var(--space-2);
  }

  .nav__menu.active .nav__link {
    padding: var(--space-3) var(--space-4);
    border-radius: var(--radius-lg);
    margin: var(--space-1) 0;
    opacity: 0;
    animation: fadeInUp 0.3s ease-out forwards;
  }

  .nav__menu.active .nav__link:nth-child(1) {
    animation-delay: 0.1s;
  }
  .nav__menu.active .nav__link:nth-child(2) {
    animation-delay: 0.15s;
  }
  .nav__menu.active .nav__link:nth-child(3) {
    animation-delay: 0.2s;
  }
  .nav__menu.active .nav__link:nth-child(4) {
    animation-delay: 0.25s;
  }
  .nav__menu.active .nav__link:nth-child(5) {
    animation-delay: 0.3s;
  }
}

/* Hero Section */
.hero {
  padding: calc(80px + var(--space-20)) 0 var(--space-20);
  background: linear-gradient(
    135deg,
    var(--color-gray-50) 0%,
    var(--color-white) 100%
  );
}

.hero__container {
  max-width: var(--container-xl);
  margin: 0 auto;
  padding: 0 var(--space-6);
  display: grid;
  grid-template-columns: 1fr;
  gap: var(--space-8);
  align-items: center;
}

@media (min-width: 768px) {
  .hero__container {
    grid-template-columns: 1fr 2fr;
    gap: var(--space-8);
  }
}

@media (min-width: 1024px) {
  .hero__container {
    gap: var(--space-10);
  }
}

.hero__content {
  text-align: center;
  animation: fadeInUp 1s ease-out;
}

@media (min-width: 768px) {
  .hero__content {
    text-align: left;
  }
}

.hero__title {
  margin-bottom: var(--space-6);
}

.hero__greeting {
  display: block;
  font-size: var(--text-lg);
  color: var(--color-primary);
  font-weight: 500;
  margin-bottom: var(--space-2);
}

.hero__name {
  display: block;
  font-size: var(--text-5xl);
  color: var(--color-gray-900);
  margin-bottom: var(--space-2);
}

.hero__role {
  display: block;
  font-size: var(--text-2xl);
  color: var(--color-gray-600);
  font-weight: 400;
}

@media (max-width: 640px) {
  .hero__name {
    font-size: var(--text-4xl);
  }

  .hero__role {
    font-size: var(--text-xl);
  }
}

.hero__image {
  display: flex;
  justify-content: center;
  animation: fadeInUp 1s ease-out 0.3s both;
}

.hero__description {
  font-size: var(--text-lg);
  margin-bottom: var(--space-8);
  max-width: 600px;
  color: var(--color-gray-600);
}

.hero__actions {
  display: flex;
  gap: var(--space-4);
  justify-content: center;
  flex-wrap: wrap;
}

@media (min-width: 768px) {
  .hero__actions {
    justify-content: flex-start;
  }
}

@media (min-width: 768px) {
  .hero__image {
    justify-content: center;
  }
}

.hero__photo {
  width: 300px;
  height: 300px;
  border-radius: var(--radius-full);
  object-fit: cover;
  box-shadow: var(--shadow-xl);
  border: 4px solid var(--color-white);
  transition: all var(--transition-normal);
  cursor: pointer;
}

.hero__photo:hover {
  transform: scale(1.05) rotate(2deg);
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
}

@media (max-width: 640px) {
  .hero__photo {
    width: 250px;
    height: 250px;
  }
}

/* Section Header */
.section-header {
  text-align: center;
  margin-bottom: var(--space-16);
  opacity: 0;
  transform: translateY(30px);
  animation: fadeInUp 0.8s ease-out forwards;
}

.section-header__title {
  margin-bottom: var(--space-4);
  position: relative;
}

.section-header__title::after {
  content: "";
  position: absolute;
  bottom: -8px;
  left: 50%;
  transform: translateX(-50%);
  width: 60px;
  height: 3px;
  background-color: var(--color-primary);
  border-radius: var(--radius-full);
}

.section-header__subtitle {
  font-size: var(--text-lg);
  color: var(--color-gray-600);
  max-width: 600px;
  margin: 0 auto;
}

/* About Section */
.about {
  padding: var(--space-20) 0;
  background-color: var(--color-white);
}

.about__container {
  max-width: var(--container-xl);
  margin: 0 auto;
  padding: 0 var(--space-6);
}

.about__content {
  display: grid;
  grid-template-columns: 1fr;
  gap: var(--space-12);
}

@media (min-width: 1024px) {
  .about__content {
    grid-template-columns: 2fr 1fr;
    gap: var(--space-16);
  }
}

.about__story,
.about__highlights {
  opacity: 0;
  transform: translateY(30px);
  transition: all 0.8s ease-out;
}

.about__story.animate-in {
  opacity: 1;
  transform: translateY(0);
  transition-delay: 0.2s;
}

.about__highlights.animate-in {
  opacity: 1;
  transform: translateY(0);
  transition-delay: 0.4s;
}

.about__story-title,
.about__highlights-title {
  font-size: var(--text-2xl);
  margin-bottom: var(--space-6);
  color: var(--color-gray-900);
  opacity: 0;
  transform: translateY(20px);
  transition: all 0.6s ease-out;
}

.about__story-title.animate-in {
  opacity: 1;
  transform: translateY(0);
  transition-delay: 0.1s;
}

.about__highlights-title.animate-in {
  opacity: 1;
  transform: translateY(0);
  transition-delay: 0.3s;
}

/* Timeline */
.timeline {
  position: relative;
  padding-left: var(--space-8);
  opacity: 0;
  transform: translateY(30px);
  transition: all 0.8s ease-out;
}

.timeline.animate-in {
  opacity: 1;
  transform: translateY(0);
  transition-delay: 0.3s;
}

.timeline::before {
  content: "";
  position: absolute;
  left: var(--space-4);
  top: 0;
  bottom: 0;
  width: 2px;
  background-color: var(--color-gray-200);
  transform: scaleY(0);
  transform-origin: top;
  transition: transform 1s ease-out;
}

.timeline.animate-in::before {
  transform: scaleY(1);
  transition-delay: 0.5s;
}

.timeline__item {
  position: relative;
  margin-bottom: var(--space-8);
  opacity: 0;
  transform: translateX(-30px);
  transition: all 0.6s ease-out;
}

.timeline__item:last-child {
  margin-bottom: 0;
}

.timeline__item.animate-in:nth-child(1) {
  opacity: 1;
  transform: translateX(0);
  transition-delay: 0.6s;
}
.timeline__item.animate-in:nth-child(2) {
  opacity: 1;
  transform: translateX(0);
  transition-delay: 0.7s;
}
.timeline__item.animate-in:nth-child(3) {
  opacity: 1;
  transform: translateX(0);
  transition-delay: 0.8s;
}
.timeline__item.animate-in:nth-child(4) {
  opacity: 1;
  transform: translateX(0);
  transition-delay: 0.9s;
}

.timeline__marker {
  position: absolute;
  left: calc(-1 * var(--space-8) + var(--space-3));
  top: var(--space-2);
  width: 12px;
  height: 12px;
  background-color: var(--color-primary);
  border-radius: var(--radius-full);
  border: 3px solid var(--color-white);
  box-shadow: var(--shadow-md);
}

.timeline__title {
  font-size: var(--text-lg);
  font-weight: 600;
  color: var(--color-gray-900);
  margin-bottom: var(--space-2);
}

.timeline__description {
  color: var(--color-gray-600);
  line-height: 1.6;
}

/* Highlights List */
.highlights-list__item {
  padding: var(--space-4);
  margin-bottom: var(--space-3);
  background-color: var(--color-gray-50);
  border-radius: var(--radius-lg);
  border-left: 4px solid var(--color-primary);
  opacity: 0;
  transform: translateX(-20px);
  transition: all var(--transition-normal);
}

.highlights-list__item.animate-in:nth-child(1) {
  opacity: 1;
  transform: translateX(0);
  transition-delay: 0.5s;
}
.highlights-list__item.animate-in:nth-child(2) {
  opacity: 1;
  transform: translateX(0);
  transition-delay: 0.6s;
}
.highlights-list__item.animate-in:nth-child(3) {
  opacity: 1;
  transform: translateX(0);
  transition-delay: 0.7s;
}
.highlights-list__item.animate-in:nth-child(4) {
  opacity: 1;
  transform: translateX(0);
  transition-delay: 0.8s;
}

.highlights-list__item:hover {
  transform: translateX(5px);
  box-shadow: var(--shadow-md);
  background-color: var(--color-white);
}

.highlights-list__item strong {
  color: var(--color-gray-900);
}

/* Skills Section */
.skills {
  padding: var(--space-20) 0;
  background-color: var(--color-gray-50);
}

.skills__container {
  max-width: var(--container-xl);
  margin: 0 auto;
  padding: 0 var(--space-6);
}

.skills__grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: var(--space-8);
}

@media (min-width: 768px) {
  .skills__grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (min-width: 1024px) {
  .skills__grid {
    grid-template-columns: repeat(3, 1fr);
  }
}

/* Skill Card */
.skill-card {
  background-color: var(--color-white);
  padding: var(--space-8);
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-md);
  text-align: center;
  transition: all var(--transition-normal);
  opacity: 0;
  transform: translateY(50px);
  animation: fadeInUp 0.6s ease-out forwards;
}

.skill-card:nth-child(1) {
  animation-delay: 0.1s;
}
.skill-card:nth-child(2) {
  animation-delay: 0.2s;
}
.skill-card:nth-child(3) {
  animation-delay: 0.3s;
}

.skill-card:hover {
  transform: translateY(-8px) scale(1.02);
  box-shadow: var(--shadow-xl);
}

.skill-card:hover .skill-card__icon img {
  transform: scale(1.1) rotate(5deg);
}

.skill-card__icon {
  margin-bottom: var(--space-6);
}

.skill-card__icon img {
  width: 80px;
  height: 80px;
  border-radius: var(--radius-lg);
  margin: 0 auto;
  object-fit: cover;
  transition: all var(--transition-normal);
}

.skill-card__title {
  font-size: var(--text-xl);
  margin-bottom: var(--space-4);
  color: var(--color-gray-900);
}

.skill-card__list {
  text-align: left;
}

.skill-card__list li {
  padding: var(--space-2) 0;
  color: var(--color-gray-600);
  border-bottom: 1px solid var(--color-gray-100);
}

.skill-card__list li:last-child {
  border-bottom: none;
}

.skill-card__list li::before {
  content: "✓";
  color: var(--color-primary);
  font-weight: bold;
  margin-right: var(--space-2);
}

/* Portfolio Section */
.portfolio {
  padding: var(--space-20) 0;
  background-color: var(--color-white);
}

.portfolio__container {
  max-width: var(--container-xl);
  margin: 0 auto;
  padding: 0 var(--space-6);
}

.portfolio__grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: var(--space-8);
}

@media (min-width: 768px) {
  .portfolio__grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

/* Project Card */
.project-card {
  background-color: var(--color-white);
  border-radius: var(--radius-xl);
  overflow: hidden;
  box-shadow: var(--shadow-md);
  transition: all var(--transition-normal);
  opacity: 0;
  transform: translateY(50px);
  animation: fadeInUp 0.6s ease-out forwards;
}

.project-card:nth-child(1) {
  animation-delay: 0.1s;
}
.project-card:nth-child(2) {
  animation-delay: 0.2s;
}
.project-card:nth-child(3) {
  animation-delay: 0.3s;
}
.project-card:nth-child(4) {
  animation-delay: 0.4s;
}

.project-card:hover {
  transform: translateY(-8px) scale(1.02);
  box-shadow: var(--shadow-xl);
}

.project-card__image {
  position: relative;
  overflow: hidden;
}

.project-card__img {
  width: 100%;
  height: 250px;
  object-fit: cover;
  transition: transform var(--transition-slow);
}

.project-card__image:hover .project-card__img {
  transform: scale(1.05);
}

.project-card__content {
  padding: var(--space-6);
}

.project-card__title {
  font-size: var(--text-xl);
  margin-bottom: var(--space-3);
  color: var(--color-gray-900);
}

.project-card__description {
  color: var(--color-gray-600);
  margin-bottom: var(--space-4);
  line-height: 1.6;
}

.project-card__tech {
  display: flex;
  flex-wrap: wrap;
  gap: var(--space-2);
}

/* Tech Tags */
.tech-tag {
  display: inline-block;
  padding: var(--space-1) var(--space-3);
  background-color: var(--color-primary);
  color: var(--color-white);
  font-size: var(--text-sm);
  border-radius: var(--radius-full);
  font-weight: 500;
}

/* Contact Section */
.contact {
  padding: var(--space-20) 0;
  background-color: var(--color-gray-50);
}

.contact__container {
  max-width: var(--container-xl);
  margin: 0 auto;
  padding: 0 var(--space-6);
}

.contact__content {
  display: grid;
  grid-template-columns: 1fr;
  gap: var(--space-12);
}

@media (min-width: 1024px) {
  .contact__content {
    grid-template-columns: 1fr 1fr;
    gap: var(--space-16);
  }
}

.contact__info-title,
.contact__form-title {
  font-size: var(--text-2xl);
  margin-bottom: var(--space-6);
  color: var(--color-gray-900);
}

.contact__details {
  font-style: normal;
  margin-bottom: var(--space-8);
}

.contact__item {
  display: flex;
  align-items: center;
  margin-bottom: var(--space-4);
  padding: var(--space-4);
  background-color: var(--color-white);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-sm);
}

.contact__label {
  min-width: 80px;
  color: var(--color-gray-700);
  margin-right: var(--space-3);
}

.contact__value {
  color: var(--color-gray-900);
}

.contact__link {
  color: var(--color-primary);
}

.contact__link:hover {
  color: var(--color-primary-dark);
  text-decoration: underline;
}

.contact__cta {
  background-color: var(--color-white);
  padding: var(--space-6);
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-md);
}

.contact__cta-text {
  margin-bottom: var(--space-6);
  color: var(--color-gray-600);
  line-height: 1.6;
}

.contact__actions {
  display: flex;
  gap: var(--space-4);
  flex-wrap: wrap;
}

.contact__form-container {
  background-color: var(--color-white);
  padding: var(--space-8);
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-md);
}

/* Form Styles */
.form-group {
  margin-bottom: var(--space-6);
  opacity: 0;
  transform: translateY(20px);
  animation: fadeInUp 0.5s ease-out forwards;
}

.form-group:nth-child(1) {
  animation-delay: 0.1s;
}
.form-group:nth-child(2) {
  animation-delay: 0.2s;
}
.form-group:nth-child(3) {
  animation-delay: 0.3s;
}
.form-group:nth-child(4) {
  animation-delay: 0.4s;
}
.form-group:nth-child(5) {
  animation-delay: 0.5s;
}

.form-label {
  display: block;
  margin-bottom: var(--space-2);
  font-weight: 500;
  color: var(--color-gray-700);
}

.form-input,
.form-textarea {
  width: 100%;
  padding: var(--space-3);
  border: 2px solid var(--color-gray-200);
  border-radius: var(--radius-lg);
  font-size: var(--text-base);
  transition: border-color var(--transition-fast);
}

.form-input:focus,
.form-textarea:focus {
  outline: none;
  border-color: var(--color-primary);
  box-shadow: 0 0 0 3px rgba(0, 0, 0, 0.1);
}

.form-textarea {
  resize: vertical;
  min-height: 120px;
}

/* Footer */
.footer {
  background-color: var(--color-gray-900);
  color: var(--color-gray-300);
  padding: var(--space-8) 0;
}

.footer__container {
  max-width: var(--container-xl);
  margin: 0 auto;
  padding: 0 var(--space-6);
  text-align: center;
}

.footer__text {
  margin: 0;
  font-size: var(--text-sm);
}

/* Utility Classes */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

.text-center {
  text-align: center;
}
.text-left {
  text-align: left;
}
.text-right {
  text-align: right;
}
.hidden {
  display: none;
}
.block {
  display: block;
}
.inline-block {
  display: inline-block;
}
.flex {
  display: flex;
}
.grid {
  display: grid;
}

/* Mobile Navigation Animations */
.nav__toggle.active {
  background-color: rgba(248, 250, 252, 0.15);
}

.nav__toggle.active .nav__toggle-line:nth-child(1) {
  transform: rotate(45deg) translate(6px, 6px);
}

.nav__toggle.active .nav__toggle-line:nth-child(2) {
  opacity: 0;
  transform: scale(0);
}

.nav__toggle.active .nav__toggle-line:nth-child(3) {
  transform: rotate(-45deg) translate(6px, -6px);
}

/* Animation Classes */
.animate-in {
  opacity: 1 !important;
  transform: translateY(0) !important;
}

/* Keyframe Animations */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInRight {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes fadeInLeft {
  from {
    opacity: 0;
    transform: translateX(-30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes scaleInY {
  from {
    transform: scaleY(0);
  }
  to {
    transform: scaleY(1);
  }
}

@keyframes slideInFromBottom {
  from {
    opacity: 0;
    transform: translateY(50px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes pulse {
  0%,
  100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
}

@keyframes bounce {
  0%,
  20%,
  53%,
  80%,
  100% {
    transform: translateY(0);
  }
  40%,
  43% {
    transform: translateY(-10px);
  }
  70% {
    transform: translateY(-5px);
  }
}

/* Scroll-triggered animations */
.animate-on-scroll {
  opacity: 0;
  transform: translateY(50px);
  transition: all 0.6s ease-out;
}

.animate-on-scroll.animate-in {
  opacity: 1;
  transform: translateY(0);
}

/* Timeline animations are now consolidated above */

/* Contact form animations are now consolidated above */

/* Navigation hover effects are now consolidated above */

/* Print Styles */
@media print {
  .header,
  .nav,
  .btn,
  .contact__form-container {
    display: none;
  }

  .hero,
  .about,
  .skills,
  .portfolio {
    page-break-inside: avoid;
  }

  /* Disable animations in print */
  *,
  *::before,
  *::after {
    animation-duration: 0s !important;
    animation-delay: 0s !important;
    transition-duration: 0s !important;
    transition-delay: 0s !important;
  }
}
