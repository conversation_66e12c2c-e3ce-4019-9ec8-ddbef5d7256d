<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta
      name="description"
      content="S.Vivekrajiv - Full Stack Web Developer & Freelancer. Expert in Next.js, React, PHP, MySQL. Creating elegant web solutions."
    />
    <meta
      name="keywords"
      content="Full Stack Developer, Web Developer, Next.js, React, PHP, MySQL, Freelancer"
    />
    <title>S.Vivekrajiv - Full Stack Web Developer</title>
    <link rel="stylesheet" href="styles/reset.css" />
    <link rel="stylesheet" href="styles/main.css" />
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link
      href="https://fonts.googleapis.com/css2?family=Lato:wght@300;400;700;900&family=Playfair+Display:wght@400;500;600&display=swap"
      rel="stylesheet"
    />
    <!-- EmailJS Library -->
    <script src="https://cdn.jsdelivr.net/npm/@emailjs/browser@3/dist/email.min.js"></script>
  </head>
  <body>
    <header class="header">
      <nav class="nav" role="navigation" aria-label="Main navigation">
        <div class="nav__container">
          <a href="#home" class="nav__logo">Vivekrajiv</a>
          <button
            class="nav__toggle"
            aria-label="Toggle navigation menu"
            aria-expanded="false"
          >
            <span class="nav__toggle-line"></span>
            <span class="nav__toggle-line"></span>
            <span class="nav__toggle-line"></span>
          </button>
          <ul class="nav__menu" role="menubar">
            <li class="nav__item" role="none">
              <a href="#home" class="nav__link" role="menuitem">Home</a>
            </li>
            <li class="nav__item" role="none">
              <a href="#about" class="nav__link" role="menuitem">About</a>
            </li>
            <li class="nav__item" role="none">
              <a href="#skills" class="nav__link" role="menuitem">Skills</a>
            </li>
            <li class="nav__item" role="none">
              <a href="#portfolio" class="nav__link" role="menuitem"
                >Portfolio</a
              >
            </li>
            <li class="nav__item" role="none">
              <a href="#contact" class="nav__link" role="menuitem">Contact</a>
            </li>
          </ul>
        </div>
      </nav>
    </header>

    <main class="main">
      <section id="home" class="hero">
        <div class="hero__container">
          <div class="hero__image">
            <img
              src="images/profile2.jpg"
              alt="S.Vivekrajiv - Professional Developer"
              class="hero__photo"
              loading="lazy"
            />
          </div>
          <div class="hero__content">
            <h1 class="hero__title">
              <span class="hero__greeting">Hello, I'm</span>
              <span class="hero__name">S.Vivekrajiv</span>
              <span class="hero__role">Full Stack Web Developer</span>
            </h1>
            <p class="hero__description">
              Transforming ideas into elegant digital solutions. With expertise
              in modern web technologies and 12+ years of professional
              experience, I create responsive, user-friendly applications that
              drive business success.
            </p>
            <div class="hero__actions">
              <a href="#portfolio" class="btn btn--secondary">View My Work</a>
              <a href="#contact" class="btn btn--secondary">Get In Touch</a>
            </div>
          </div>
        </div>
      </section>

      <section id="about" class="about">
        <div class="about__container">
          <header class="section-header">
            <h2 class="section-header__title">About Me</h2>
            <p class="section-header__subtitle">
              My journey from Computer Science to Banking to Web Development
            </p>
          </header>

          <div class="about__content">
            <div class="about__story">
              <h3 class="about__story-title">My Professional Journey</h3>
              <div class="timeline">
                <article class="timeline__item">
                  <div class="timeline__marker"></div>
                  <div class="timeline__content">
                    <h4 class="timeline__title">
                      M.Sc Computer Science Graduate
                    </h4>
                    <p class="timeline__description">
                      Built a strong foundation in computer science principles,
                      algorithms, and programming.
                    </p>
                  </div>
                </article>

                <article class="timeline__item">
                  <div class="timeline__marker"></div>
                  <div class="timeline__content">
                    <h4 class="timeline__title">
                      Full Stack Developer at Nextshore Pvt Ltd
                    </h4>
                    <p class="timeline__description">
                      Developed enterprise-level web applications using modern
                      technologies and frameworks.
                    </p>
                  </div>
                </article>

                <article class="timeline__item">
                  <div class="timeline__marker"></div>
                  <div class="timeline__content">
                    <h4 class="timeline__title">
                      Banking Professional (12+ Years)
                    </h4>
                    <p class="timeline__description">
                      Gained valuable business acumen and client relationship
                      skills in the financial sector.
                    </p>
                  </div>
                </article>

                <article class="timeline__item">
                  <div class="timeline__marker"></div>
                  <div class="timeline__content">
                    <h4 class="timeline__title">Freelance Web Developer</h4>
                    <p class="timeline__description">
                      Currently creating custom web solutions for businesses
                      across various industries.
                    </p>
                  </div>
                </article>
              </div>
            </div>

            <div class="about__highlights">
              <h3 class="about__highlights-title">
                What I Bring to Your Project
              </h3>
              <ul class="highlights-list">
                <li class="highlights-list__item">
                  <strong>Technical Expertise:</strong> Modern web technologies
                  and best practices
                </li>
                <li class="highlights-list__item">
                  <strong>Business Understanding:</strong> 12+ years of
                  professional experience
                </li>
                <li class="highlights-list__item">
                  <strong>Client Focus:</strong> Dedicated to delivering
                  solutions that exceed expectations
                </li>
                <li class="highlights-list__item">
                  <strong>Quality Assurance:</strong> Rigorous testing and
                  attention to detail
                </li>
              </ul>
            </div>
          </div>
        </div>
      </section>

      <section id="skills" class="skills">
        <div class="skills__container">
          <header class="section-header">
            <h2 class="section-header__title">Technical Skills</h2>
            <p class="section-header__subtitle">
              Technologies I use to bring your ideas to life
            </p>
          </header>

          <div class="skills__grid">
            <article class="skill-card">
              <div class="skill-card__icon">
                <img
                  src="https://images.unsplash.com/photo-1627398242454-45a1465c2479?w=100&h=100&fit=crop"
                  alt="Frontend Development"
                />
              </div>
              <h3 class="skill-card__title">Frontend Development</h3>
              <ul class="skill-card__list">
                <li>Semantic HTML5</li>
                <li>Modern CSS (Nesting, Layers, Container Queries)</li>
                <li>JavaScript (ES6+)</li>
                <li>React & Next.js</li>
              </ul>
            </article>

            <article class="skill-card">
              <div class="skill-card__icon">
                <img
                  src="https://images.unsplash.com/photo-1558494949-ef010cbdcc31?w=100&h=100&fit=crop"
                  alt="Backend Development"
                />
              </div>
              <h3 class="skill-card__title">Backend Development</h3>
              <ul class="skill-card__list">
                <li>PHP</li>
                <li>MySQL Database</li>
                <li>RESTful APIs</li>
                <li>Server Management</li>
              </ul>
            </article>

            <article class="skill-card">
              <div class="skill-card__icon">
                <img
                  src="https://images.unsplash.com/photo-1460925895917-afdab827c52f?w=100&h=100&fit=crop"
                  alt="Full Stack Solutions"
                />
              </div>
              <h3 class="skill-card__title">Full Stack Solutions</h3>
              <ul class="skill-card__list">
                <li>E-Commerce Platforms</li>
                <li>Business Applications</li>
                <li>Responsive Design</li>
                <li>Performance Optimization</li>
              </ul>
            </article>
          </div>
        </div>
      </section>

      <section id="portfolio" class="portfolio">
        <div class="portfolio__container">
          <header class="section-header">
            <h2 class="section-header__title">Featured Projects</h2>
            <p class="section-header__subtitle">
              Showcasing my recent work and achievements
            </p>
          </header>

          <div class="portfolio__grid">
            <article class="project-card">
              <div class="project-card__image">
                <img
                  src="https://images.unsplash.com/photo-**********-6726b3ff858f?w=600&h=400&fit=crop"
                  alt="Balance Sheet Analyzer Application"
                  class="project-card__img"
                />
              </div>
              <div class="project-card__content">
                <h3 class="project-card__title">Balance Sheet Analyzer</h3>
                <p class="project-card__description">
                  Advanced financial analysis tool that helps businesses
                  understand their financial health through automated
                  calculations and visual reports.
                </p>
                <div class="project-card__tech">
                  <span class="tech-tag">PHP</span>
                  <span class="tech-tag">MySQL</span>
                  <span class="tech-tag">JavaScript</span>
                  <span class="tech-tag">CSS</span>
                </div>
              </div>
            </article>

            <article class="project-card">
              <div class="project-card__image">
                <img
                  src="https://images.unsplash.com/photo-**********-0cfed4f6a45d?w=600&h=400&fit=crop"
                  alt="Accounting Software Interface"
                  class="project-card__img"
                />
              </div>
              <div class="project-card__content">
                <h3 class="project-card__title">Accounting Software</h3>
                <p class="project-card__description">
                  Comprehensive accounting solution with invoice management,
                  expense tracking, and financial reporting capabilities for
                  small to medium businesses.
                </p>
                <div class="project-card__tech">
                  <span class="tech-tag">React</span>
                  <span class="tech-tag">Next.js</span>
                  <span class="tech-tag">MySQL</span>
                  <span class="tech-tag">PHP</span>
                </div>
              </div>
            </article>

            <article class="project-card">
              <div class="project-card__image">
                <img
                  src="https://images.unsplash.com/photo-**********-ec7c0e9f34b1?w=600&h=400&fit=crop"
                  alt="E-Commerce Website"
                  class="project-card__img"
                />
              </div>
              <div class="project-card__content">
                <h3 class="project-card__title">E-Commerce Platform</h3>
                <p class="project-card__description">
                  Modern e-commerce solution with shopping cart, payment
                  integration, inventory management, and responsive design for
                  optimal user experience.
                </p>
                <div class="project-card__tech">
                  <span class="tech-tag">Next.js</span>
                  <span class="tech-tag">React</span>
                  <span class="tech-tag">MySQL</span>
                  <span class="tech-tag">CSS</span>
                </div>
              </div>
            </article>

            <article class="project-card">
              <div class="project-card__image">
                <img
                  src="https://images.unsplash.com/photo-1629909613654-28e377c37b09?w=600&h=400&fit=crop"
                  alt="Dental Hospital Website"
                  class="project-card__img"
                />
              </div>
              <div class="project-card__content">
                <h3 class="project-card__title">Dental Hospital Website</h3>
                <p class="project-card__description">
                  Professional healthcare website with appointment booking,
                  service showcase, patient portal, and responsive design
                  optimized for medical practices.
                </p>
                <div class="project-card__tech">
                  <span class="tech-tag">HTML5</span>
                  <span class="tech-tag">CSS</span>
                  <span class="tech-tag">JavaScript</span>
                  <span class="tech-tag">PHP</span>
                </div>
              </div>
            </article>
          </div>
        </div>
      </section>

      <section id="contact" class="contact">
        <div class="contact__container">
          <header class="section-header">
            <h2 class="section-header__title">Let's Work Together</h2>
            <p class="section-header__subtitle">
              Ready to bring your project to life? Get in touch!
            </p>
          </header>

          <div class="contact__content">
            <div class="contact__info">
              <h3 class="contact__info-title">Contact Information</h3>
              <address class="contact__details">
                <div class="contact__item">
                  <strong class="contact__label">Name:</strong>
                  <span class="contact__value">S.Vivekrajiv</span>
                </div>
                <div class="contact__item">
                  <strong class="contact__label">Mobile:</strong>
                  <a
                    href="tel:+919786470779"
                    class="contact__value contact__link"
                    >+91 9786470779</a
                  >
                </div>
                <div class="contact__item">
                  <strong class="contact__label">Email:</strong>
                  <a
                    href="mailto:<EMAIL>"
                    class="contact__value contact__link"
                    ><EMAIL></a
                  >
                </div>
              </address>

              <div class="contact__cta">
                <p class="contact__cta-text">
                  Whether you need a new website, want to modernize an existing
                  application, or require ongoing development support, I'm here
                  to help turn your vision into reality.
                </p>
                <div class="contact__actions">
                  <a href="tel:+919786470779" class="btn btn--secondary"
                    >Call Now</a
                  >
                  <!--<a
                    href="mailto:<EMAIL>"
                    class="btn btn--secondary"
                    >Send Email</a>-->
                </div>
              </div>
            </div>

            <div class="contact__form-container">
              <form class="contact__form" action="#" method="post">
                <h3 class="contact__form-title">Send a Mail</h3>

                <div class="form-group">
                  <label for="name" class="form-label">Your Name</label>
                  <input
                    type="text"
                    id="name"
                    name="name"
                    class="form-input"
                    required
                  />
                </div>

                <div class="form-group">
                  <label for="email" class="form-label">Your Email</label>
                  <input
                    type="email"
                    id="email"
                    name="email"
                    class="form-input"
                    required
                  />
                </div>

                <div class="form-group">
                  <label for="subject" class="form-label">Subject</label>
                  <input
                    type="text"
                    id="subject"
                    name="subject"
                    class="form-input"
                    required
                  />
                </div>

                <div class="form-group">
                  <label for="message" class="form-label">Message</label>
                  <textarea
                    id="message"
                    name="message"
                    class="form-textarea"
                    rows="5"
                    required
                  ></textarea>
                </div>

                <button type="submit" class="btn btn--primary btn--full">
                  Send Mail
                </button>
              </form>
            </div>
          </div>
        </div>
      </section>
    </main>

    <footer class="footer">
      <div class="footer__container">
        <p class="footer__text">
          &copy; 2024 S.Vivekrajiv. All rights reserved. | Full Stack Web
          Developer & Freelancer
        </p>
      </div>
    </footer>

    <script src="scripts/emailjs-config.js"></script>
    <script src="scripts/main.js"></script>
  </body>
</html>
